##################################################################
#  MindfulJourney — Full Build with Gemini CLI (Dark-Mode AI SaaS)
##################################################################

GOAL
  Build a production-ready web app where users journal (text, image, audio),
  then receive Google Gemini-powered prompts, sentiment charts, and goal
  tracking. Tech stack: Next.js 14 App Router, Tailwind 3 + shadcn/ui,
  Supabase v2 (Auth + Storage), Stripe Checkout 2025-05-01, Workbox PWA,
  Vitest + Playwright tests, Vercel deployment.

TOOLS
  allowed:
    - browser.open, browser.find          # doc scraping
    - code.write, code.exec               # file edits
    - shell.run                           # npm, git, vercel commands
    - memory.write, memory.read           # store URLs, partial plans
  denied:
    - shell.run: ["rm -rf /"]

PLACEHOLDERS
  <supabase_url>  <supabase_anon_key>  <supabase_service_role>
  <stripe_pk>     <stripe_sk>          <gemini_api_key>  <vercel_token>

PHASE 1 — RESEARCH  ⇒ researchstack.md
  • browser.open docs for: Next.js 14 App Router:contentReference[oaicite:4]{index=4},
    Tailwind install:contentReference[oaicite:5]{index=5}:contentReference[oaicite:6]{index=6}, shadcn/ui init:contentReference[oaicite:7]{index=7}:contentReference[oaicite:8]{index=8},
    Supabase Auth Next.js guide:contentReference[oaicite:9]{index=9}:contentReference[oaicite:10]{index=10},
    Stripe Checkout docs:contentReference[oaicite:11]{index=11}:contentReference[oaicite:12]{index=12},
    Workbox caching strategies:contentReference[oaicite:13]{index=13}:contentReference[oaicite:14]{index=14},
    Vitest guide:contentReference[oaicite:15]{index=15}:contentReference[oaicite:16]{index=16},
    Vercel CLI deploy docs:contentReference[oaicite:17]{index=17}.
  • Copy **verbatim** install commands, env templates, and example snippets
    into researchstack.md, grouped by library; cite each URL.

PHASE 2 — DESIGN  ⇒ design.md
  • Information architecture (Home, Auth, Dashboard, AI Insights,
    Goals, Billing, Settings, Docs, Support).
  • Postgres schema tables: users, entries, goals, insights, media,
    subscriptions.
  • RBAC: Admin | User | Coach (read-only insights).
  • Stripe tier matrix (Free / $5 Starter / $15 Pro / Enterprise custom).
  • Dark-theme CSS variables:
      --bg-default:#121212; --bg-surface1:#1E1F26; --text-primary:#E0E0E0;
      --accent-primary:#7F5AF0; --accent-secondary:#00FFD5.
  • PWA offline queue (Workbox + Dexie); push notifications.
  • Observability stack (PostHog, Sentry).
  • Threat model & GDPR endpoints (`/api/user/export`, `/api/user/delete`).
  • **Every section must reference a snippet URL from researchstack.md.**
  • No code may be generated until this file exists.

PHASE 3 — SCAFFOLD
  shell.run >>
  ```bash
  npx create-next-app@latest mindfuljourney \
    --typescript --tailwind --eslint --app --src-dir
  pnpm dlx shadcn@latest init -y
• Add Tailwind config content paths and shadcn/ui preset.

PHASE 4 — DEVELOP
• code.write pages & API routes; integrate:
– Supabase OAuth (Google, GitHub)
supabase.com
,
– Google Gemini wrapper /lib/ai/gemini.ts (stream, exponential back-off),
– Stripe Checkout session creation & webhooks
docs.stripe.com
,
– Workbox service-worker with network-first strategy
developer.chrome.com
,
– i18n via next-intl (en, es, ar-RTL).
• Add Vitest unit tests & Playwright e2e; storybook stories.

PHASE 5 — VALIDATE ⇒ validation_report.md
• shell.run pnpm test, pnpm playwright test.
• Run Lighthouse; fail build if PWA <95 or Perf <90.
• Run secret & CVE scan via npx snyk test.

PHASE 6 — DEPLOY
shell.run >>

bash
Copy
Edit
vercel pull
vercel --prod --token $VERCEL_TOKEN
• Save live URL to memory key deploy_url; append to deployment.md.

SUCCESS CRITERIA
☑ researchstack.md & design.md complete, cross-cited
☑ Tests, Lighthouse, security scans pass
☑ Live site reachable at deploy_url
☑ README, Storybook, docs generated

BEGIN → Execute PHASE 1_RESEARCH now, echo confidence and blockers
before acting, then proceed phase-by-phase respecting guard-rails.

ruby
Copy
Edit

---

### Key doc references used
* Gemini CLI npm install guide :contentReference[oaicite:21]{index=21}  
* Next.js App Router docs :contentReference[oaicite:22]{index=22}  
* Tailwind CSS install steps :contentReference[oaicite:23]{index=23}  
* shadcn/ui Next.js init :contentReference[oaicite:24]{index=24}  
* Supabase Auth helpers for Next.js :contentReference[oaicite:25]{index=25}  
* Stripe Checkout Sessions API :contentReference[oaicite:26]{index=26}  
* Workbox caching strategies :contentReference[oaicite:27]{index=27}  
* Vitest docs and CLI :contentReference[oaicite:28]{index=28}  
* Vercel CLI deployment docs :contentReference[oaicite:29]{index=29}  
* Reddit install-tips thread for CLI troubleshooting :contentReference[oaicite:30]{index=30}  
