# Design

## Information Architecture

*   **Home:** Landing page with a call to action.
*   **Auth:** Login and registration pages.
*   **Dashboard:** Main view for authenticated users, showing recent entries and insights.
*   **AI Insights:** A dedicated page to view AI-generated prompts and sentiment analysis.
*   **Goals:** A page for users to set and track their goals.
*   **Billing:** A page for users to manage their subscription.
*   **Settings:** A page for users to manage their account settings.
*   **Docs:** A page for API and user documentation.
*   **Support:** A page for users to contact support.

## Postgres Schema

*   **users:**
    *   `id` (uuid, primary key)
    *   `email` (text, unique)
    *   `created_at` (timestamp with time zone)
*   **entries:**
    *   `id` (uuid, primary key)
    *   `user_id` (uuid, foreign key to `users.id`)
    *   `text_content` (text)
    *   `created_at` (timestamp with time zone)
*   **goals:**
    *   `id` (uuid, primary key)
    *   `user_id` (uuid, foreign key to `users.id`)
    *   `description` (text)
    *   `completed` (boolean)
    *   `created_at` (timestamp with time zone)
*   **insights:**
    *   `id` (uuid, primary key)
    *   `entry_id` (uuid, foreign key to `entries.id`)
    *   `sentiment` (text)
    *   `prompt` (text)
    *   `created_at` (timestamp with time zone)
*   **media:**
    *   `id` (uuid, primary key)
    *   `entry_id` (uuid, foreign key to `entries.id`)
    *   `type` (text) - 'image' or 'audio'
    *   `url` (text)
    *   `created_at` (timestamp with time zone)
*   **subscriptions:**
    *   `id` (uuid, primary key)
    *   `user_id` (uuid, foreign key to `users.id`)
    *   `stripe_customer_id` (text)
    *   `stripe_subscription_id` (text)
    *   `status` (text)
    *   `created_at` (timestamp with time zone)

## RBAC

*   **Admin:** Can manage all users and data.
*   **User:** Can manage their own data.
*   **Coach:** Can view insights for assigned users.

## Stripe Tier Matrix

*   **Free:**
    *   10 journal entries per month
    *   Basic AI insights
*   **$5 Starter:**
    *   Unlimited journal entries
    *   Advanced AI insights
    *   Goal tracking
*   **$15 Pro:**
    *   All Starter features
    *   Audio journaling
    *   Priority support
*   **Enterprise:**
    *   Custom features and pricing

## Dark-Theme CSS Variables

```css
:root {
  --bg-default: #121212;
  --bg-surface1: #1E1F26;
  --text-primary: #E0E0E0;
  --accent-primary: #7F5AF0;
  --accent-secondary: #00FFD5;
}
```

## PWA Offline Queue

*   **Workbox Strategy:** Network First for dynamic content, Cache First for static assets.
*   **Offline Storage:** Dexie.js for queueing mutations (new entries, goals) to be sent to the server when the connection is restored.

## Observability Stack

*   **PostHog:** For product analytics and feature flags.
*   **Sentry:** For error tracking and performance monitoring.

## Threat Model & GDPR Endpoints

*   **Threats:**
    *   Data breaches (mitigated by Supabase's security features)
    *   Cross-site scripting (XSS) (mitigated by Next.js's built-in protections)
    *   Cross-site request forgery (CSRF) (mitigated by Supabase's token-based authentication)
*   **GDPR Endpoints:**
    *   `/api/user/export`: Export all user data as a JSON file.
    *   `/api/user/delete`: Delete a user's account and all associated data.
