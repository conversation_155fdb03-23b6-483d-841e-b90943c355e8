# Research Stack

## Next.js 14 App Router

*   **Official Documentation:** [https://nextjs.org/docs/app](https://nextjs.org/docs/app)
*   **Key Features:**
    *   File-system based router.
    *   Utilizes React Server Components, Suspense, and Server Actions.
    *   Supports layouts, nested routes, and error handling.
*   **Installation:**
    ```bash
    npx create-next-app@latest --typescript --tailwind --eslint --app --src-dir
    ```

## Tailwind CSS

*   **Official Documentation:** [https://tailwindcss.com/docs/guides/nextjs](https://tailwindcss.com/docs/guides/nextjs)
*   **Installation:**
    ```bash
    pnpm install -D tailwindcss postcss autoprefixer
    npx tailwindcss init -p
    ```
*   **Configuration:**
    *   Add content paths to `tailwind.config.js`:
        ```javascript
        /** @type {import('tailwindcss').Config} */
        module.exports = {
          content: [
            './app/**/*.{js,ts,jsx,tsx,mdx}',
            './pages/**/*.{js,ts,jsx,tsx,mdx}',
            './components/**/*.{js,ts,jsx,tsx,mdx}',
            './src/**/*.{js,ts,jsx,tsx,mdx}',
          ],
          theme: {
            extend: {},
          },
          plugins: [],
        }
        ```
    *   Add Tailwind directives to `globals.css`:
        ```css
        @tailwind base;
        @tailwind components;
        @tailwind utilities;
        ```

## shadcn/ui

*   **Official Documentation:** [https://ui.shadcn.com/docs/installation/next](https://ui.shadcn.com/docs/installation/next)
*   **Installation:**
    ```bash
    pnpm dlx shadcn-ui@latest init
    ```

## Supabase Auth with Next.js

*   **Official Documentation:** [https://supabase.com/docs/guides/auth/server-side-rendering](https://supabase.com/docs/guides/auth/server-side-rendering)
*   **Installation:**
    ```bash
    pnpm install @supabase/auth-helpers-nextjs @supabase/supabase-js
    ```
*   **Environment Variables:**
    ```
    NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
    NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
    ```

## Stripe Checkout

*   **Official Documentation:** [https://stripe.com/docs/checkout/quickstart](https://stripe.com/docs/checkout/quickstart)
*   **Installation:**
    ```bash
    pnpm install @stripe/stripe-js
    ```
*   **API Reference:** [https://stripe.com/docs/api/checkout/sessions](https://stripe.com/docs/api/checkout/sessions)

## Workbox

*   **Official Documentation:** [https://developer.chrome.com/docs/workbox/](https://developer.chrome.com/docs/workbox/)
*   **Caching Strategies:** [https://developer.chrome.com/docs/workbox/caching-strategies-overview](https://developer.chrome.com/docs/workbox/caching-strategies-overview)
    *   Cache First
    *   Network First
    *   Stale-While-Revalidate
    *   Cache Only
    *   Network Only

## Vitest

*   **Official Documentation:** [https://vitest.dev/guide/](https://vitest.dev/guide/)
*   **Installation:**
    ```bash
    pnpm install -D vitest
    ```
*   **Configuration:**
    *   Add a `test` property to `vite.config.js` or create a `vitest.config.ts` file.

## Vercel CLI

*   **Official Documentation:** [https://vercel.com/docs/cli](https://vercel.com/docs/cli)
*   **Installation:**
    ```bash
    npm install -g vercel
    ```
*   **Deployment:**
    ```bash
    vercel login
    vercel --prod
    ```
